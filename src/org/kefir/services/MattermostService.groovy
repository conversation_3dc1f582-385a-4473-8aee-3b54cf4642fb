package org.kefir.services

import hudson.Util

class MattermostService {
    private final def script
    private Integer maxMsgLen = 100

    MattermostService(script) {
        this.script = script
    }

    /**
     * Отправляет уведомление об успешной сборке
     */
    Boolean sendSuccessNotification(Map config = [:]) {
        if (isNotificationDisabled(config, 'SUCCESS')) {
            script.jenkinsUtils.markStageSkipped()
            return true
        }

        String msg = formatHeader(
            result: 'successful',
            scmVars: config?.scmVars,
            params: config?.params
        )

        if (config?.message) {
            msg += config.message + '\n'
        }

        msg += formatParameters(config)
        msg += formatFooter()

        return script.mattermostSend(
            channel: config?.channel ?: '#jenkins',
            color: 'good',
            message: msg
        )
    }

    /**
     * Отправляет уведомление об ошибке сборки
     */
    Boolean sendFailureNotification(Map config = [:]) {
        if (isNotificationDisabled(config, 'FAILURE')) {
            return true
        }

        String msg = formatHeader(
            result: 'failed',
            scmVars: config?.scmVars,
            params: config?.params
        )

        msg += config?.error?.message ? "due to: *${config.error.message}*\n" : ''
        msg += formatFooter()

        return script.mattermostSend(
            channel: config?.channel ?: '#jenkins',
            color: 'danger',
            message: msg
        )
    }

    /**
     * Проверяет, отключены ли уведомления
     */
    private Boolean isNotificationDisabled(Map config, String type) {
        if (!config?.params) {
            return false
        }

        def params = config.params
        return (params?.binding?.hasVariable('MATTERMOST_DISABLE') && params.MATTERMOST_DISABLE) ||
               (params?.binding?.hasVariable("MATTERMOST_DISABLE_${type}") && params["MATTERMOST_DISABLE_${type}"])
    }

    /**
     * Форматирует заголовок сообщения
     */
    private String formatHeader(Map args) {
        String msg = "Job <${script.env.JOB_URL}|${script.env.JOB_NAME}> <${script.env.BUILD_URL}|${script.env.BUILD_DISPLAY_NAME}> ${args?.result}\n"
        
        if (args?.scmVars) {
            msg += "on branch: <${args.scmVars.GIT_URL}/-/tree/${args.scmVars.GIT_BRANCH_REFSPEC}|${args.scmVars.GIT_BRANCH_REFSPEC}>, commit: <${args.scmVars.GIT_URL}/-/commit/${args.scmVars.GIT_COMMIT}|${args.scmVars.GIT_COMMIT_SHORT_SHA}>"
            if (args?.params && args.params?.binding?.hasVariable('SHARED_LIB_BRANCH')) {
                msg += ", shared library: <https://${script.gitlabUtils.gitHostname}/${script.gitlabUtils.sharedLibraryPath}/-/tree/${args.params.SHARED_LIB_BRANCH}|${args.params.SHARED_LIB_BRANCH}>"
            }
            msg += '\n'
        }

        String changes = getChangeString()
        msg += changes ? "changes:\n\n${changes}\n" : ''
        return msg
    }

    /**
     * Форматирует параметры сборки
     */
    private String formatParameters(Map config) {
        if (!config?.params) {
            return ''
        }

        String msg = 'parameters: '
        config.params.binding?.variables?.each { key, value ->
            if (value != null) {
                msg += "_${key}_=*$value* "
            }
        }
        msg += '\n'
        return msg
    }

    /**
     * Форматирует подвал сообщения
     */
    private String formatFooter() {
        String msg = ''
        String buildDurationString = Util.getTimeSpanString(script.currentBuild.duration)
        String timings = "at: _${script.dateUtils.currentDate}_ and took: _${buildDurationString}_"
        Integer causesSize = script.currentBuild?.buildCauses?.size()
        
        if (causesSize) {
            script.currentBuild.buildCauses.each {
                if (it?.shortDescription) {
                    msg += (causesSize > 1 ? ' • ' : '') + it.shortDescription + (causesSize > 1 ? '\n' : " $timings")
                }
            }
            msg += causesSize > 1 ? timings : ''
        } else {
            msg += timings
        }
        return msg
    }

    /**
     * Получает строку с изменениями SCM
     */
    private String getChangeString() {
        String changeString = ''
        script.echo('Gathering SCM changes')
        def changeLogSets = script.currentBuild.changeSets
        
        for (int i = 0; i < changeLogSets.size(); i++) {
            def entries = changeLogSets[i].items
            for (int j = 0; j < entries.length; j++) {
                def entry = entries[j]
                def truncated_msg = entry.msg.take(this.maxMsgLen)
                def truncated_sha = entry.commitId.take(8)
                changeString += " • ${truncated_msg} [<mailto:${entry.authorEmail}|${entry.author}>] #<https://${script.gitlabUtils.gitHostname}/search?scope=commits&search=${entry.commitId}|${truncated_sha}>\n"
            }
        }

        return changeString
    }
}
