// Простой тест для проверки MattermostService
// Этот файл можно использовать в Jenkins pipeline для тестирования функциональности

pipeline {
    agent any
    
    parameters {
        choice(
            name: 'TEST_TYPE',
            choices: ['success', 'failure', 'custom', 'short_status', 'all'],
            description: 'Тип теста для выполнения'
        )
        string(
            name: 'TEST_CHANNEL',
            defaultValue: '#jenkins-test',
            description: 'Канал для тестовых сообщений'
        )
        booleanParam(
            name: 'MATTERMOST_DISABLE',
            defaultValue: false,
            description: 'Отключить уведомления Mattermost'
        )
    }
    
    stages {
        stage('Test MattermostService') {
            steps {
                script {
                    def mattermostService = new org.kefir.services.MattermostService(this)
                    
                    switch(params.TEST_TYPE) {
                        case 'success':
                            testSuccessNotification(mattermostService)
                            break
                        case 'failure':
                            testFailureNotification(mattermostService)
                            break
                        case 'custom':
                            testCustomMessage(mattermostService)
                            break
                        case 'short_status':
                            testShortStatus(mattermostService)
                            break
                        case 'all':
                            testSuccessNotification(mattermostService)
                            sleep(2)
                            testFailureNotification(mattermostService)
                            sleep(2)
                            testCustomMessage(mattermostService)
                            sleep(2)
                            testShortStatus(mattermostService)
                            break
                        default:
                            error("Неизвестный тип теста: ${params.TEST_TYPE}")
                    }
                }
            }
        }
    }
}

def testSuccessNotification(mattermostService) {
    echo "Тестирование sendSuccessNotification..."
    
    def result = mattermostService.sendSuccessNotification([
        channel: params.TEST_CHANNEL,
        message: 'Это тестовое сообщение об успешной сборке 🎉',
        scmVars: [
            GIT_URL: 'https://gitlab.company.com/test/repo',
            GIT_BRANCH_REFSPEC: 'main',
            GIT_COMMIT: 'abc123def456',
            GIT_COMMIT_SHORT_SHA: 'abc123d'
        ],
        params: params,
        containerImages: [
            [imageUri: 'registry.company.com/test-app', imageTag: 'test-1.0.0']
        ],
        paramsFilter: ['TEST_TYPE', 'TEST_CHANNEL']
    ])
    
    if (result) {
        echo "✅ Тест sendSuccessNotification прошел успешно"
    } else {
        error("❌ Тест sendSuccessNotification провалился")
    }
}

def testFailureNotification(mattermostService) {
    echo "Тестирование sendFailureNotification..."
    
    def result = mattermostService.sendFailureNotification([
        channel: params.TEST_CHANNEL,
        error: [message: 'Это тестовая ошибка для демонстрации'],
        scmVars: [
            GIT_URL: 'https://gitlab.company.com/test/repo',
            GIT_BRANCH_REFSPEC: 'feature/test',
            GIT_COMMIT: 'def456abc789',
            GIT_COMMIT_SHORT_SHA: 'def456a'
        ],
        params: params
    ])
    
    if (result) {
        echo "✅ Тест sendFailureNotification прошел успешно"
    } else {
        error("❌ Тест sendFailureNotification провалился")
    }
}

def testCustomMessage(mattermostService) {
    echo "Тестирование sendCustomMessage..."
    
    def result = mattermostService.sendCustomMessage([
        channel: params.TEST_CHANNEL,
        message: 'Это кастомное тестовое сообщение 📝',
        color: 'warning'
    ])
    
    if (result) {
        echo "✅ Тест sendCustomMessage прошел успешно"
    } else {
        error("❌ Тест sendCustomMessage провалился")
    }
}

def testShortStatus(mattermostService) {
    echo "Тестирование sendShortStatus..."
    
    def result = mattermostService.sendShortStatus([
        channel: params.TEST_CHANNEL,
        message: '📊 Короткое статусное сообщение для теста',
        status: 'testing'
    ])
    
    if (result) {
        echo "✅ Тест sendShortStatus прошел успешно"
    } else {
        error("❌ Тест sendShortStatus провалился")
    }
}
