import org.kefir.services.DocBuilder
import org.kefir.services.NpmService
import org.kefir.services.MattermostService

def call(Map config) {
    def npmService = new NpmService(this)
    def docBuilder = new DocBuilder(this)
    def mattermostService = new MattermostService(this)

    pipeline {
        agent { node { label config.agentLabel ?: jenkinsUtils.determineAgent() } }

        stages {
            stage('Публикация приложения') {
                when {
                    expression { config.app.enabled }
                }
                steps {
                    script {
                        npmService.publishNpm([
                            verdaccioInstance: config.app.verdaccioInstance,
                            nodeJsTool: config.app.nodeJsTool
                        ])
                    }
                }
            }
            stage('Публикация документации в Confluence') {
                when {
                    expression { config.docs.enabled }
                }
                steps {
                    script {
                        docBuilder.publishDocs([
                            confluenceSpaceName: config.docs.confluenceSpaceName,
                            confluenceParentPageID: config.docs.confluenceParentPageID,
                            documentationDirectoryPath: config.docs.documentationDirectoryPath,
                            documentationLabel: config.docs.documentationLabel,
                            documentationTitle: config.docs.documentationTitle,
                            documentationHomePagePath: config.docs.documentationHomePagePath
                        ])
                    }
                }
            }
        }

        post {
            success {
                script {
                    if (config.notifications?.mattermost?.enabled) {
                        mattermostService.sendSuccessNotification([
                            channel: config.notifications.mattermost.channel,
                            message: config.notifications.mattermost.successMessage,
                            scmVars: scmUtils.getScmVars(),
                            params: params,
                            containerImages: config.notifications.mattermost.containerImages,
                            paramsFilter: config.notifications.mattermost.paramsFilter
                        ])
                    }
                }
            }
            failure {
                script {
                    if (config.notifications?.mattermost?.enabled) {
                        mattermostService.sendFailureNotification([
                            channel: config.notifications.mattermost.channel,
                            error: [message: currentBuild.description ?: 'Build failed'],
                            scmVars: scmUtils.getScmVars(),
                            params: params
                        ])
                    }
                }
            }
            unstable {
                script {
                    if (config.notifications?.mattermost?.enabled) {
                        mattermostService.sendCustomMessage([
                            channel: config.notifications.mattermost.channel,
                            message: "Build ${env.JOB_NAME} #${env.BUILD_NUMBER} is unstable",
                            color: 'warning'
                        ])
                    }
                }
            }
        }
    }
}