# MattermostService

Класс `MattermostService` предоставляет удобный интерфейс для отправки уведомлений в Mattermost из Jenkins pipeline. Он автоматически форматирует сообщения с информацией о сборке, изменениях в коде и параметрах pipeline.

## Возможности

- ✅ Отправка уведомлений об успешной сборке
- ❌ Отправка уведомлений об ошибках сборки
- ⚠️ Отправка уведомлений о нестабильной сборке
- 📝 Отправка кастомных сообщений
- 📊 Отправка коротких статусных сообщений
- 🔧 Гибкая конфигурация каналов и форматирования
- 🚫 Возможность отключения уведомлений через параметры

## Использование в buildPipeline

### Базовая конфигурация

```groovy
def config = [
    // ... другие настройки ...
    
    notifications: [
        mattermost: [
            enabled: true,
            channel: '#jenkins-builds'
        ]
    ]
]

buildPipeline(config)
```

### Расширенная конфигурация

```groovy
def config = [
    // ... другие настройки ...
    
    notifications: [
        mattermost: [
            enabled: true,
            channel: '#deployment-notifications',
            successMessage: 'Приложение успешно собрано и готово к деплою',
            containerImages: [
                [imageUri: 'registry.company.com/app', imageTag: env.BUILD_NUMBER]
            ],
            paramsFilter: ['BUILD', 'DEPLOY', 'ENVIRONMENT']
        ]
    ]
]

buildPipeline(config)
```

## Прямое использование в pipeline

```groovy
pipeline {
    agent any
    
    stages {
        stage('Build') {
            steps {
                script {
                    def mattermostService = new org.kefir.services.MattermostService(this)
                    
                    mattermostService.sendCustomMessage([
                        channel: '#dev-team',
                        message: "Начинается сборка ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                        color: 'good'
                    ])
                }
            }
        }
    }
}
```

## API методы

### sendSuccessNotification(Map config)

Отправляет уведомление об успешной сборке.

**Параметры:**
- `channel` (String, optional) - канал для отправки (по умолчанию #jenkins)
- `message` (String, optional) - дополнительное сообщение
- `scmVars` (Map, optional) - переменные SCM
- `params` (Map, optional) - параметры сборки
- `containerImages` (List, optional) - список образов контейнеров
- `paramsFilter` (List, optional) - фильтр параметров для отображения

**Пример:**
```groovy
mattermostService.sendSuccessNotification([
    channel: '#releases',
    message: 'Новая версия готова к деплою!',
    scmVars: scmUtils.getScmVars(),
    params: params,
    containerImages: [
        [imageUri: 'registry.company.com/app', imageTag: '1.2.3']
    ]
])
```

### sendFailureNotification(Map config)

Отправляет уведомление об ошибке сборки.

**Параметры:**
- `channel` (String, optional) - канал для отправки (по умолчанию #jenkins)
- `error` (Map, optional) - объект ошибки с полем `message`
- `scmVars` (Map, optional) - переменные SCM
- `params` (Map, optional) - параметры сборки

**Пример:**
```groovy
mattermostService.sendFailureNotification([
    channel: '#alerts',
    error: [message: 'Compilation failed'],
    scmVars: scmUtils.getScmVars(),
    params: params
])
```

### sendCustomMessage(Map config)

Отправляет кастомное сообщение.

**Параметры:**
- `channel` (String, optional) - канал для отправки (по умолчанию #jenkins)
- `message` (String, required) - текст сообщения
- `color` (String, optional) - цвет сообщения (good, warning, danger)

**Пример:**
```groovy
mattermostService.sendCustomMessage([
    channel: '#dev-team',
    message: 'Деплой в staging окружение завершен',
    color: 'good'
])
```

### sendShortStatus(Map config)

Отправляет короткое статусное сообщение.

**Параметры:**
- `channel` (String, optional) - канал для отправки (по умолчанию #jenkins)
- `message` (String, required) - текст сообщения
- `status` (String, optional) - статус сборки

**Пример:**
```groovy
mattermostService.sendShortStatus([
    channel: '#qa-team',
    message: '✅ Все тесты прошли успешно'
])
```

## Отключение уведомлений

Уведомления можно отключить через параметры pipeline:

- `MATTERMOST_DISABLE=true` - отключает все уведомления
- `MATTERMOST_DISABLE_SUCCESS=true` - отключает уведомления об успешной сборке
- `MATTERMOST_DISABLE_FAILURE=true` - отключает уведомления об ошибках

## Форматирование сообщений

Сервис автоматически форматирует сообщения, включая:

- Информацию о job и build
- Ссылки на ветку и коммит
- Список изменений в коде
- Информацию о параметрах сборки
- Время выполнения и причину запуска
- Информацию о контейнерных образах (если указаны)

## Цвета сообщений

- `good` (зеленый) - для успешных операций
- `warning` (желтый) - для предупреждений
- `danger` (красный) - для ошибок

## Требования

- Jenkins с установленным плагином Mattermost
- Настроенная интеграция с Mattermost сервером
- Доступ к каналам Mattermost для Jenkins бота
