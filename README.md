# Jenkins Shared Libraries - Ke<PERSON>r

Документация по использованию Jenkins Shared Libraries для проектов Kefir.

## Оглавление

- [Обзор](#обзор)
- [Классы валидаторов](#классы-валидаторов)
  - [BaseValidator](#basevalidator)
  - [PackageJsonValidator](#packagejsonvalidator)
  - [InitPyValidator](#initpyvalidator)
  - [MdFilesValidator](#mdfilesvalidator)
- [Pipeline методы](#pipeline-методы)
  - [projectPipeline](#projectpipeline)
  - [validatePipeline](#validatepipeline)
  - [buildPipeline](#buildpipeline)
- [Сервисы](#сервисы)
  - [NpmService](#npmservice)
  - [CommentBuilder](#commentbuilder)
  - [MattermostService](#mattermostservice)
- [Примеры использования](#примеры-использования)
- [Обработка ошибок](#обработка-ошибок)
- [Рекомендации](#рекомендации)

## Обзор

Jenkins Shared Libraries предоставляют набор валидаторов и pipeline методов для автоматизации процессов валидации, сборки и публикации проектов.

Библиотека поддерживает проекты с файлами `package.json` и `__init__.py`.

## Классы валидаторов

### BaseValidator

Базовый абстрактный класс для всех валидаторов.

**Методы:**
- `createResult(boolean success, String log)` - создает стандартизированный результат валидации

**Структура результата:**
```groovy
[
    success: boolean,        // true если валидация прошла успешно
    log: String,            // сообщение о результате валидации
    validatorType: String   // тип валидатора (автоматически определяется)
]
```

### PackageJsonValidator

Валидатор для проверки файлов `package.json`.

**Конструктор:**
```groovy
PackageJsonValidator(Script script)
```

**Методы валидации:**

#### `checkName()`
Проверяет корректность поля `name` в package.json.

**Правила валидации:**
- Поле должно существовать и быть заполнено
- Должно начинаться с префикса `com.kefir.`
- Должно заканчиваться названием репозитория
- Должно быть в формате kebab-case
- Не должно содержать несколько дефисов подряд

**Пример корректного значения:**
```json
{
  "name": "com.kefir.my-awesome-project"
}
```

#### `checkVersion()`
Проверяет корректность поля `version` в package.json.

**Правила валидации:**
- Поле должно существовать и быть заполнено
- Должно соответствовать стандарту SemVer
- Версия должна быть больше версии в целевой ветке
- Проверяет существование пакета в репозитории Verdaccio(в разработке)

**Пример:**
```json
{
  "version": "1.2.3"
}
```

#### `checkAuthor()`
Проверяет корректность поля `author`.

**Поддерживаемые форматы:**
```json
{
  "author": "John Doe"
}
```
или
```json
{
  "author": {
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

#### `checkRepositoryUrl()`
Проверяет корректность URL репозитория.

**Ожидаемый формат:**
```json
{
  "repository": "************************:group/project.git"
}
```

#### `checkDocumentationUrl()`
Проверяет корректность URL документации.

**Ожидаемый формат:**
```json
{
  "documentationUrl": "https://gitlab.i.kefir.games/group/project/-/blob/master/README.md"
}
```

#### `checkChangelogUrl()`
Проверяет корректность URL changelog.

**Ожидаемый формат:**
```json
{
  "changelogUrl": "https://gitlab.i.kefir.games/group/project/-/blob/master/CHANGELOG.md"
}
```

### InitPyValidator

Валидатор для проверки файлов `__init__.py`.

**Конструктор:**
```groovy
InitPyValidator(Script script)
```

**Методы валидации:**

#### `checkBlInfo()`
Проверяет наличие блока `bl_info` в файле.

**Пример корректного блока:**
```python
bl_info = {
    "name": "My Awesome Addon",
    "version": (1, 2, 3),
    "author": "John Doe"
}
```

#### `checkName()`
Проверяет наличие и заполненность поля `name` в блоке `bl_info`.

#### `checkVersion()`
Проверяет корректность поля `version` в блоке `bl_info`.

**Правила валидации:**
- Должно быть в формате кортежа: `(major, minor, patch)`
- Версия должна быть больше версии в целевой ветке

#### `checkAuthor()`
Проверяет наличие и заполненность поля `author` в блоке `bl_info`.

### MdFilesValidator

Валидатор для проверки Markdown файлов (в разработке).

**Конструктор:**
```groovy
MdFilesValidator(Script script)
```

*Примечание: Методы валидации находятся в разработке.*

## Pipeline методы

### projectPipeline

Основной метод пайплайна, который автоматически выбирает тип выполнения в зависимости от контекста.

**Использование:**
```groovy
@Library('devops-shared') _

projectPipeline([
    validateConfig: [
        // конфигурация для валидации
    ],
    buildConfig: [
        // конфигурация для сборки
    ]
])
```

**Логика выбора:**
- Если `env.CHANGE_ID` существует (Merge Request) → запускает `validatePipeline`
- Если `env.BRANCH_NAME == 'master'` → запускает `buildPipeline`
- Иначе → пропускает выполнение

### validatePipeline

Pipeline для валидации проектов в Merge Request'ах.

**Конфигурация:**
```groovy
validatePipeline([
    agentLabel: 'gce',  // опционально, по умолчанию определяется автоматически
    checks: [
        // Проверки для __init__.py
        initPyBlInfo: true,
        initPyName: true,
        initPyAuthor: true,
        initPyVersion: true,
        
        // Проверки для package.json
        packageJsonVersion: true,
        packageJsonName: true,
        packageJsonRepositoryUrl: true,
        packageJsonDocumentationUrl: true,
        packageJsonChangelogUrl: true
    ]
])
```

**Особенности:**
- Автоматически добавляет комментарии в GitLab Merge Request с результатами валидации
- При неуспешной валидации помечает сборку как `UNSTABLE`
- Использует `skipDefaultCheckout()` для оптимизации

### buildPipeline

Pipeline для сборки и публикации проектов.

**Конфигурация:**
```groovy
buildPipeline([
    agentLabel: 'gce',  // опционально
    app: [
        enabled: true,
        verdaccioInstance: 'verdaccio',
        nodeJsTool: 'node24.3.0'
    ],
    docs: [
        enabled: true,
        confluenceSpaceName: 'DEV',
        confluenceParentPageID: '12345',
        documentationDirectoryPath: 'docs',
        documentationLabel: 'my-project',
        documentationTitle: 'My Project Documentation',
        documentationHomePagePath: 'docs/README.md'
    ],
    notifications: [
        mattermost: [
            enabled: true,
            channel: '#jenkins-builds',
            successMessage: 'Сборка успешно завершена! 🎉'
        ]
    ]
])
```

## Сервисы

### NpmService

Сервис для работы с npm пакетами и Verdaccio.

**Методы:**

#### `publishNpm(Map config)`
Публикует npm пакет в Verdaccio.

```groovy
npmService.publishNpm([
    verdaccioInstance: 'verdaccio',
    nodeJsTool: 'node24.3.0'
])
```

#### `checkPackageExists(String name, String version, String verdaccioInstance, String nodeJsTool)` (в разработке)
Проверяет существование пакета в репозитории.

**Возвращает:**
```groovy
[
    exists: boolean,    // true если пакет существует
    version: String,    // версия пакета (если найден)
    error: String       // сообщение об ошибке (если произошла)
]
```

### CommentBuilder

Сервис для построения комментариев валидации в GitLab.

**Методы:**

#### `buildFullComment(List<Map> validationResults)`
Создает полный комментарий с результатами всех валидаций.

**Пример результата:**
```markdown
### 📦 Проверка package.json
- ✅ Поле name валидно: com.kefir.my-project
- ❌ Версия 1.0.0 должна быть больше чем 1.0.0 в ветке master

### 🐍 Проверка __init__.py
- ✅ Блок bl_info найден
- ✅ Поле name в блоке bl_info валидно

### 📊 Итоги
- Успешных проверок: 3
- Неуспешных проверок: 1
- Общее количество: 4

⚠️ Требуется исправление ошибок!
```

### MattermostService

Сервис для отправки уведомлений в Mattermost о результатах сборки.

**Методы:**

#### `sendSuccessNotification(Map config)`
Отправляет уведомление об успешной сборке.

```groovy
mattermostService.sendSuccessNotification([
    channel: '#releases',
    message: 'Новая версия готова к деплою!',
    scmVars: scmUtils.getScmVars(),
    params: params
])
```

#### `sendFailureNotification(Map config)`
Отправляет уведомление об ошибке сборки.

```groovy
mattermostService.sendFailureNotification([
    channel: '#alerts',
    error: [message: 'Compilation failed'],
    scmVars: scmUtils.getScmVars(),
    params: params
])
```

#### `sendCustomMessage(Map config)`
Отправляет кастомное сообщение.

```groovy
mattermostService.sendCustomMessage([
    channel: '#dev-team',
    message: 'Деплой в staging окружение завершен',
    color: 'good'
])
```

**Интеграция в buildPipeline:**
Сервис автоматически интегрируется в `buildPipeline` через конфигурацию `notifications.mattermost`.

**Отключение уведомлений:**
Используйте параметры pipeline:
- `MATTERMOST_DISABLE=true` - отключает все уведомления
- `MATTERMOST_DISABLE_SUCCESS=true` - отключает уведомления об успешной сборке
- `MATTERMOST_DISABLE_FAILURE=true` - отключает уведомления об ошибках

## Примеры использования

### Простая валидация для Node.js проекта

```groovy
@Library('devops-shared') _

projectPipeline([
    validateConfig: [
        checks: [
            packageJsonVersion: true,
            packageJsonName: true,
            packageJsonRepositoryUrl: true
        ]
    ],
    buildConfig: [
        app: [
            enabled: true,
            verdaccioInstance: 'verdaccio',
            nodeJsTool: 'node24.3.0'
        ]
    ]
])
```

### Валидация для Python проекта

```groovy
@Library('devops-shared') _

projectPipeline([
    validateConfig: [
        checks: [
            initPyBlInfo: true,
            initPyName: true,
            initPyVersion: true,
            initPyAuthor: true
        ]
    ]
])
```

### Полная конфигурация с валидацией, сборкой и публикацией документации

```groovy
@Library('devops-shared') _

projectPipeline([
    validateConfig: [
        agentLabel: 'docker',
        checks: [
            // Все проверки включены
            initPyBlInfo: true,
            initPyName: true,
            initPyAuthor: true,
            initPyVersion: true,
            packageJsonVersion: true,
            packageJsonName: true,
            packageJsonRepositoryUrl: true,
            packageJsonDocumentationUrl: true,
            packageJsonChangelogUrl: true
        ]
    ],
    buildConfig: [
        app: [
            enabled: true,
            verdaccioInstance: 'verdaccio',
            nodeJsTool: 'node24.3.0'
        ],
        docs: [
            enabled: true,
            confluenceSpaceName: 'DOCS',
            confluenceParentPageID: '67890',
            documentationDirectoryPath: 'documentation',
            documentationLabel: 'my-awesome-project',
            documentationTitle: 'My Awesome Project',
            documentationHomePagePath: 'documentation/index.md'
        ],
        notifications: [
            mattermost: [
                enabled: true,
                channel: '#project-updates',
                successMessage: 'Проект успешно собран и документация обновлена!',
                paramsFilter: ['BUILD', 'DEPLOY', 'ENVIRONMENT']
            ]
        ]
    ]
])
```

## Обработка ошибок

### Типичные проблемы и решения

#### 1. Ошибка инициализации валидатора
```
Ошибка инициализации PackageJsonValidator: Project not found
```
**Решение:** Убедитесь, что проект существует в GitLab и доступен для Jenkins.

#### 2. Ошибка проверки существования пакета в Verdaccio
```
Предупреждение: Не удалось проверить существование пакета my-package@1.0.0: Connection timeout
```
**Решение:** Проверьте доступность Verdaccio и корректность конфигурации.

#### 3. Ошибка валидации версии
```
Версия 1.0.0 должна быть больше чем 1.0.0 в ветке master
```
**Решение:** Увеличьте версию в соответствии с SemVer.

## Рекомендации

### Настройка проекта

1. **Для Node.js проектов:**
   - Убедитесь, что `package.json` содержит все необходимые поля
   - Используйте семантическое версионирование

2. **Для Python проектов:**
   - Добавьте блок `bl_info` в `__init__.py`
   - Используйте формат версии `(major, minor, patch)`

### Производительность

- Валидаторы используют ленивую инициализацию
- Файлы загружаются из GitLab только при необходимости

### Интеграция с GitLab

Библиотека автоматически интегрируется с GitLab для:
- Получения содержимого файлов из репозитория
- Определения ID и названия проекта
- Добавления комментариев в Merge Request'ы

### Константы и конфигурация

Основные константы определены в:
- `ValidatorsConstants` - паттерны валидации, префиксы
- `GitlabConstants` - URL и настройки GitLab
- `DockerConstants` - образы для документации

### Расширение функциональности

Для добавления новых валидаторов:

1. Создайте класс, наследующий `BaseValidator`
2. Реализуйте методы валидации
3. Добавьте вызовы в `validatePipeline`
4. Обновите `CommentBuilder` для новых типов

**Пример нового валидатора:**
```groovy
class CustomValidator extends BaseValidator {
    CustomValidator(Script script) {
        super(script)
    }

    Map checkCustomRule() {
        // логика валидации
        return createResult(true, "Проверка пройдена")
    }
}
```

## FAQ

**Q: Какие проверки включены по умолчанию?**

A: Никакие. По умолчанию все проверки отключены.


**Q: Можно ли запустить только определенные валидации?**

A: Да, используйте конфигурацию `checks` с нужными флагами:
```groovy
validatePipeline([
    checks: [
        packageJsonVersion: true,
        packageJsonName: false  // отключено
    ]
])
```

**Q: Как добавить кастомную проверку?**

A: Создайте новый валидатор или расширьте существующий:
```groovy
class MyValidator extends BaseValidator {
    Map checkCustomRule() {
        // ваша логика
        return createResult(success, message)
    }
}
```

**Q: Почему валидация не работает в feature ветках?**

A: `validatePipeline` запускается только для Merge Request'ов (когда `env.CHANGE_ID` существует).

**Q: Как настроить разные конфигурации для разных проектов?**

A: Используйте условную логику в Jenkinsfile:
```groovy
def config = [:]
if (env.JOB_NAME.contains('frontend')) {
    config.checks = [packageJsonVersion: true]
} else {
    config.checks = [initPyVersion: true]
}
projectPipeline([validateConfig: config])
```
