@Library('devops-shared') _

/**
 * Example <PERSON><PERSON>le demonstrating Mattermost notifications
 * for npm package builds and deployments
 */

projectPipeline([
    // Configuration for validation pipeline (runs on merge requests)
    validateConfig: [
        agentLabel: 'gce',
        checks: [
            // Package.json validations
            packageJson: [
                enabled: true,
                version: true,
                name: true,
                author: true,
                repositoryUrl: true,
                documentationUrl: true,
                changelogUrl: true
            ]
        ]
    ],
    
    // Configuration for build pipeline (runs on master branch)
    buildConfig: [
        agentLabel: 'gce',
        
        // NPM package publishing
        app: [
            enabled: true,
            verdaccioInstance: 'verdaccio',
            nodeJsTool: 'node24.3.0'
        ],
        
        // Documentation publishing to Confluence
        docs: [
            enabled: true,
            confluenceSpaceName: 'DEV',
            confluenceParentPageID: '12345',
            documentationDirectoryPath: 'docs',
            documentationLabel: 'my-package',
            documentationTitle: 'My Package Documentation',
            documentationHomePagePath: 'docs/README.md'
        ],
        
        // Mattermost notifications
        notifications: [
            enabled: true,                    // Enable notifications
            channel: '#npm-releases'          // Target channel
        ]
    ]
])

/*
 * Alternative approach: Custom pipeline with manual notification control
 */

// pipeline {
//     agent { label 'gce' }
//     
//     stages {
//         stage('Build') {
//             steps {
//                 sh 'npm install'
//                 sh 'npm run build'
//                 sh 'npm test'
//             }
//         }
//         
//         stage('Publish') {
//             when {
//                 branch 'master'
//             }
//             steps {
//                 script {
//                     def npmService = new org.kefir.services.NpmService(this)
//                     def result = npmService.publishNpm([
//                         verdaccioInstance: 'verdaccio',
//                         nodeJsTool: 'node24.3.0'
//                     ])
//                     
//                     if (result.success) {
//                         echo "Package published successfully: ${result.log}"
//                     } else {
//                         error("Failed to publish package: ${result.log}")
//                     }
//                 }
//             }
//         }
//     }
//     
//     post {
//         success {
//             script {
//                 // Send success notification with package info and changelog
//                 mattermostUtils.packagePublished([
//                     channel: '#npm-releases',
//                     branch: env.BRANCH_NAME
//                 ])
//             }
//         }
//         
//         failure {
//             script {
//                 // Send failure notification
//                 mattermostUtils.buildFailed([
//                     channel: '#build-alerts',
//                     error: [message: "Build failed in stage: ${env.STAGE_NAME}"]
//                 ])
//             }
//         }
//         
//         unstable {
//             script {
//                 // Send unstable notification
//                 mattermostUtils.sendBuildStatus([
//                     channel: '#build-alerts',
//                     status: 'unstable'
//                 ])
//             }
//         }
//     }
// }

/*
 * Expected notification format for successful build:
 * 
 * 📦 **Package My Awesome Package has been updated to version 1.2.3**
 * 
 * **Changelog:**
 * ```
 * ### Fixed
 * * All documentation links replaced with relative links
 * * Changed image insertion syntax for correct documentation rendering
 * 
 * ### Added
 * * New feature for better user experience
 * ```
 * 
 * **Build Information:**
 * • Job: [my-project/master](http://jenkins.example.com/job/my-project/job/master/)
 * • Build: [#42](http://jenkins.example.com/job/my-project/job/master/42/)
 * • Branch: master
 */
