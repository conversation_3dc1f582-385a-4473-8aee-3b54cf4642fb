// Пример конфигурации для использования MattermostService в buildPipeline

// Пример 1: Базовая конфигурация с уведомлениями в Mattermost
def config = [
    agentLabel: 'docker',
    
    // Конфигурация приложения
    app: [
        enabled: true,
        verdaccioInstance: 'verdaccio-config',
        nodeJsTool: 'NodeJS-18'
    ],
    
    // Конфигурация документации
    docs: [
        enabled: true,
        confluenceSpaceName: 'DEV',
        confluenceParentPageID: '12345',
        documentationDirectoryPath: './docs',
        documentationLabel: 'api-docs',
        documentationTitle: 'API Documentation',
        documentationHomePagePath: './docs/index.md'
    ],
    
    // Конфигурация уведомлений в Mattermost
    notifications: [
        mattermost: [
            enabled: true,
            channel: '#jenkins-builds',  // Канал для отправки уведомлений
            successMessage: 'Сборка успешно завершена! 🎉',  // Дополнительное сообщение для успешной сборки
            paramsFilter: ['BUILD', 'DEPLOY', 'ENVIRONMENT']  // Фильтр параметров для отображения
        ]
    ]
]

// Вызов buildPipeline с конфигурацией
buildPipeline(config)

// Пример 2: Расширенная конфигурация с контейнерными образами
def advancedConfig = [
    agentLabel: 'kubernetes',
    
    app: [
        enabled: true,
        verdaccioInstance: 'verdaccio-prod',
        nodeJsTool: 'NodeJS-20'
    ],
    
    docs: [
        enabled: false
    ],
    
    notifications: [
        mattermost: [
            enabled: true,
            channel: '#deployment-notifications',
            successMessage: 'Приложение успешно собрано и готово к деплою',
            containerImages: [
                [imageUri: 'registry.company.com/app', imageTag: env.BUILD_NUMBER],
                [imageUri: 'registry.company.com/app-worker', imageTag: env.BUILD_NUMBER]
            ],
            paramsFilter: ['BUILD', 'DEPLOY', 'ENVIRONMENT', 'VERSION']
        ]
    ]
]

buildPipeline(advancedConfig)

// Пример 3: Использование MattermostService напрямую в pipeline
pipeline {
    agent any
    
    stages {
        stage('Build') {
            steps {
                script {
                    def mattermostService = new org.kefir.services.MattermostService(this)
                    
                    // Отправка кастомного сообщения о начале сборки
                    mattermostService.sendCustomMessage([
                        channel: '#dev-team',
                        message: "Начинается сборка ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                        color: 'good'
                    ])
                    
                    // Ваша логика сборки здесь
                    sh 'echo "Building application..."'
                }
            }
        }
        
        stage('Test') {
            steps {
                script {
                    def mattermostService = new org.kefir.services.MattermostService(this)
                    
                    try {
                        // Ваши тесты здесь
                        sh 'echo "Running tests..."'
                        
                        // Отправка сообщения об успешном прохождении тестов
                        mattermostService.sendShortStatus([
                            channel: '#qa-team',
                            message: "✅ Тесты для ${env.JOB_NAME} #${env.BUILD_NUMBER} прошли успешно"
                        ])
                    } catch (Exception e) {
                        // Отправка сообщения об ошибке в тестах
                        mattermostService.sendCustomMessage([
                            channel: '#qa-team',
                            message: "❌ Тесты для ${env.JOB_NAME} #${env.BUILD_NUMBER} провалились: ${e.message}",
                            color: 'danger'
                        ])
                        throw e
                    }
                }
            }
        }
    }
    
    post {
        success {
            script {
                def mattermostService = new org.kefir.services.MattermostService(this)
                mattermostService.sendSuccessNotification([
                    channel: '#general',
                    message: 'Новая версия приложения готова!',
                    scmVars: scmUtils.getScmVars(),
                    params: params
                ])
            }
        }
        failure {
            script {
                def mattermostService = new org.kefir.services.MattermostService(this)
                mattermostService.sendFailureNotification([
                    channel: '#alerts',
                    error: [message: currentBuild.description ?: 'Сборка завершилась с ошибкой'],
                    scmVars: scmUtils.getScmVars(),
                    params: params
                ])
            }
        }
    }
}

// Пример 4: Отключение уведомлений через параметры
// В параметрах pipeline можно добавить:
// - MATTERMOST_DISABLE: true (отключает все уведомления)
// - MATTERMOST_DISABLE_SUCCESS: true (отключает уведомления об успешной сборке)
// - MATTERMOST_DISABLE_FAILURE: true (отключает уведомления об ошибках)

def configWithDisableOptions = [
    agentLabel: 'docker',
    
    app: [
        enabled: true,
        verdaccioInstance: 'verdaccio-config',
        nodeJsTool: 'NodeJS-18'
    ],
    
    docs: [
        enabled: false
    ],
    
    notifications: [
        mattermost: [
            enabled: true,
            channel: '#jenkins-builds'
            // Уведомления будут отключены, если в параметрах pipeline установлен MATTERMOST_DISABLE=true
        ]
    ]
]

buildPipeline(configWithDisableOptions)
