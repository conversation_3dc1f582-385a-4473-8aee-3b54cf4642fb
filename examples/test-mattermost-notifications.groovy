/**
 * Test script for Mattermost notification functionality
 * This script can be used to test the notification service in a Jenkins environment
 */

@Library('devops-shared') _

import org.kefir.services.MattermostNotificationService

pipeline {
    agent any
    
    parameters {
        choice(
            name: 'TEST_TYPE',
            choices: ['SUCCESS', 'FAILURE', 'CUSTOM'],
            description: 'Type of notification to test'
        )
        string(
            name: 'CHANNEL',
            defaultValue: '#jenkins-test',
            description: 'Mattermost channel for test notifications'
        )
        booleanParam(
            name: 'MATTERMOST_DISABLE',
            defaultValue: false,
            description: 'Disable all Mattermost notifications'
        )
    }
    
    stages {
        stage('Setup Test Environment') {
            steps {
                script {
                    echo "Testing Mattermost notifications..."
                    echo "Test type: ${params.TEST_TYPE}"
                    echo "Channel: ${params.CHANNEL}"
                    echo "Notifications disabled: ${params.MATTERMOST_DISABLE}"
                }
            }
        }
        
        stage('Create Test Package.json') {
            steps {
                script {
                    // Create a mock package.json for testing
                    def testPackageJson = [
                        name: 'com.kefir.test-package',
                        version: '1.2.3',
                        displayName: 'Test Package for Mattermost',
                        author: 'Jenkins Test',
                        description: 'A test package for Mattermost notification testing'
                    ]
                    
                    writeJSON file: 'package.json', json: testPackageJson
                    echo "Created test package.json"
                }
            }
        }
        
        stage('Create Test Changelog') {
            steps {
                script {
                    def testChangelog = '''# Changelog

## 1.2.3

### Fixed

* All documentation links replaced with relative links
* Changed image insertion syntax for correct documentation rendering in Confluence

### Added

* New notification service for Mattermost integration
* Enhanced error handling for build failures

## 1.2.2

### Fixed

* Display of Delete button in addon list for Addon Manager

## 1.2.1

### Fixed

* Minor bug fixes and improvements
'''
                    
                    writeFile file: 'CHANGELOG.md', text: testChangelog
                    echo "Created test CHANGELOG.md"
                }
            }
        }
        
        stage('Test Notification Service') {
            steps {
                script {
                    def notificationService = new MattermostNotificationService(this)
                    
                    switch (params.TEST_TYPE) {
                        case 'SUCCESS':
                            echo "Testing success notification..."
                            def result = notificationService.sendPackagePublishedNotification([
                                channel: params.CHANNEL,
                                branch: 'master'
                            ])
                            echo "Success notification result: ${result}"
                            break
                            
                        case 'FAILURE':
                            echo "Testing failure notification..."
                            def result = notificationService.sendBuildFailedNotification([
                                channel: params.CHANNEL,
                                branch: 'master'
                            ], [message: 'Test build failure for notification testing'])
                            echo "Failure notification result: ${result}"
                            break
                            
                        case 'CUSTOM':
                            echo "Testing custom notification..."
                            mattermostUtils.sendCustomMessage([
                                channel: params.CHANNEL,
                                message: '🧪 **Test notification from Jenkins**\n\nThis is a test of the Mattermost notification system.\n\n**Test Details:**\n• Job: ' + env.JOB_NAME + '\n• Build: ' + env.BUILD_NUMBER + '\n• Time: ' + new Date().toString(),
                                color: 'good'
                            ])
                            break
                    }
                }
            }
        }
        
        stage('Test Utility Functions') {
            steps {
                script {
                    echo "Testing mattermostUtils functions..."
                    
                    // Test package published notification
                    echo "Testing packagePublished utility..."
                    def result1 = mattermostUtils.packagePublished([
                        channel: params.CHANNEL + '-utils',
                        branch: 'master'
                    ])
                    echo "packagePublished result: ${result1}"
                    
                    // Test custom message
                    echo "Testing sendCustomMessage utility..."
                    def result2 = mattermostUtils.sendCustomMessage([
                        channel: params.CHANNEL + '-utils',
                        message: '📋 **Utility function test**\n\nTesting the mattermostUtils.sendCustomMessage function.',
                        color: 'warning'
                    ])
                    echo "sendCustomMessage result: ${result2}"
                }
            }
        }
        
        stage('Test Build Status Notifications') {
            steps {
                script {
                    echo "Testing build status notifications..."
                    
                    // Test different status types
                    ['success', 'unstable'].each { status ->
                        echo "Testing status: ${status}"
                        mattermostUtils.sendBuildStatus([
                            channel: params.CHANNEL + '-status',
                            status: status
                        ])
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo "Cleaning up test files..."
                sh 'rm -f package.json CHANGELOG.md'
            }
        }
        
        success {
            script {
                mattermostUtils.sendCustomMessage([
                    channel: params.CHANNEL,
                    message: '✅ **Mattermost notification test completed successfully**\n\nAll notification functions are working correctly.',
                    color: 'good'
                ])
            }
        }
        
        failure {
            script {
                mattermostUtils.sendCustomMessage([
                    channel: params.CHANNEL,
                    message: '❌ **Mattermost notification test failed**\n\nSome notification functions may not be working correctly. Check the build logs for details.',
                    color: 'danger'
                ])
            }
        }
    }
}

/*
 * Manual testing commands (can be run in Jenkins Script Console):
 * 
 * // Test notification service directly
 * @Library('devops-shared') _
 * import org.kefir.services.MattermostNotificationService
 * 
 * def service = new MattermostNotificationService(this)
 * def result = service.sendPackagePublishedNotification([
 *     channel: '#test',
 *     branch: 'master'
 * ])
 * println result
 * 
 * // Test utility functions
 * mattermostUtils.sendCustomMessage([
 *     channel: '#test',
 *     message: 'Test message from script console',
 *     color: 'good'
 * ])
 */
